import logging
import os
import tempfile
import shutil
from typing import Optional
import asyncio

from fastapi import FastAPI, UploadFile, Form, Request, HTTPException
from fastapi.responses import FileResponse
import soundfile as sf

# Try to import Ray components
try:
    import ray
    from ray import serve
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False

from f5_tts_api.f5tts_wrapper import F5TTSWrapper

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

app = FastAPI(title="F5-TTS Web API with Ray Serve")

# Configuration
USE_RAY_SERVE = os.getenv("USE_RAY_SERVE", "false").lower() == "true"
RAY_SERVE_URL = os.getenv("RAY_SERVE_URL", "http://127.0.0.1:8000/f5tts")

# Initialize TTS wrapper or Ray client
if USE_RAY_SERVE and RAY_AVAILABLE:
    logger.info("Using Ray Serve for F5-TTS inference")
    tts = None  # Will use Ray Serve client
    
    # Try to connect to Ray cluster
    try:
        ray.init(address="auto", ignore_reinit_error=True)
        logger.info("Connected to Ray cluster")
    except Exception as e:
        logger.warning(f"Could not connect to Ray cluster: {e}")
        logger.info("Falling back to direct wrapper")
        USE_RAY_SERVE = False
        tts = F5TTSWrapper(model_type="F5-TTS_v1")
else:
    logger.info("Using direct F5TTSWrapper for inference")
    tts = F5TTSWrapper(model_type="F5-TTS_v1")


async def call_ray_serve_synthesis(
    ref_audio_path: str,
    ref_text: Optional[str],
    gen_text: str,
    model_type: Optional[str] = "F5-TTS_v1",
    remove_silence: Optional[bool] = False,
    seed: Optional[int] = -1,
    cross_fade_duration: Optional[float] = 0.15,
    nfe_step: Optional[int] = 32,
    speed: Optional[float] = 1.0,
):
    """Call Ray Serve deployment for synthesis"""
    try:
        # Get Ray Serve deployment handle
        deployment_handle = serve.get_deployment("f5tts-service").get_handle()
        
        # Prepare request data
        request_data = {
            "ref_audio_path": ref_audio_path,
            "ref_text": ref_text or "",
            "gen_text": gen_text,
            "model_type": model_type,
            "remove_silence": remove_silence,
            "seed": seed,
            "cross_fade_duration": cross_fade_duration,
            "nfe_step": nfe_step,
            "speed": speed,
        }
        
        # Call the deployment
        result = await deployment_handle.synthesize.remote(request_data)
        
        if result.get("success", False):
            return result
        else:
            raise Exception(result.get("error", "Unknown error from Ray Serve"))
            
    except Exception as e:
        logger.error(f"Ray Serve call failed: {e}")
        raise


@app.middleware("http")
async def log_requests(request: Request, call_next):
    logger.info("Incoming request: %s %s", request.method, request.url)
    response = await call_next(request)
    logger.info("Completed request with status code: %d", response.status_code)
    return response


@app.post("/synthesize/")
async def synthesize_speech(
    ref_audio: UploadFile,
    ref_text: Optional[str] = Form(None),
    gen_text: str = Form(...),
    model_type: Optional[str] = Form("F5-TTS_v1"),
    remove_silence: Optional[bool] = Form(False),
    seed: Optional[int] = Form(-1),
    cross_fade_duration: Optional[float] = Form(0.15),
    nfe_step: Optional[int] = Form(32),
    speed: Optional[float] = Form(1.0),
):
    """
    Synthesize speech using F5-TTS with Ray Serve or direct wrapper
    """
    logger.info("Endpoint '/synthesize/' called.")
    logger.info("Received file: %s", ref_audio.filename)
    logger.info("ref_text: %s", ref_text)
    logger.info("gen_text preview: %s", gen_text[:50])
    logger.info("Parameters - model_type: %s, remove_silence: %s, seed: %s, cross_fade_duration: %s, nfe_step: %s, speed: %s",
                model_type, remove_silence, seed, cross_fade_duration, nfe_step, speed)
    logger.info("Using Ray Serve: %s", USE_RAY_SERVE)

    try:
        # Save uploaded audio to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_audio:
            shutil.copyfileobj(ref_audio.file, tmp_audio)
            tmp_audio_path = tmp_audio.name
            logger.info("Saved reference audio to: %s", tmp_audio_path)

        if USE_RAY_SERVE and RAY_AVAILABLE:
            # Use Ray Serve deployment
            result = await call_ray_serve_synthesis(
                ref_audio_path=tmp_audio_path,
                ref_text=ref_text,
                gen_text=gen_text,
                model_type=model_type,
                remove_silence=remove_silence,
                seed=seed,
                cross_fade_duration=cross_fade_duration,
                nfe_step=nfe_step,
                speed=speed,
            )
            
            output_path = result["output_path"]
            sample_rate = result["sample_rate"]
            used_seed = result["used_seed"]
            
            logger.info("Ray Serve synthesis successful. Sample rate: %d, Used seed: %s", sample_rate, used_seed)
            
        else:
            # Use direct wrapper
            (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = tts.infer(
                ref_audio_orig=tmp_audio_path,
                ref_text=ref_text,
                gen_text=gen_text,
                model_type=model_type,
                remove_silence=remove_silence,
                seed=seed,
                cross_fade_duration=cross_fade_duration,
                nfe_step=nfe_step,
                speed=speed,
                show_info=logger.info,
            )

            logger.info("Direct synthesis successful. Sample rate: %d, Used seed: %s", sample_rate, used_seed)
            logger.info("Processed ref_text: %s", processed_ref_text)
            if spectrogram_path:
                logger.info("Spectrogram saved to: %s", spectrogram_path)

            # Save output audio
            output_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
            sf.write(output_path, audio_data, sample_rate)
            logger.info("Generated speech saved to: %s", output_path)

        # Clean up temporary reference audio file
        try:
            os.unlink(tmp_audio_path)
        except OSError:
            logger.warning("Could not delete temporary file: %s", tmp_audio_path)

        return FileResponse(output_path, media_type="audio/wav", filename="output.wav")

    except Exception as e:
        logger.exception("Error during speech synthesis: %s", str(e))
        # Clean up temporary files on error
        try:
            if 'tmp_audio_path' in locals():
                os.unlink(tmp_audio_path)
        except OSError:
            pass
        raise HTTPException(status_code=500, detail=f"Speech synthesis failed: {str(e)}")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        if USE_RAY_SERVE and RAY_AVAILABLE:
            # Check Ray Serve deployment health
            try:
                deployment_handle = serve.get_deployment("f5tts-service").get_handle()
                health_result = await deployment_handle.health_check.remote()
                return {
                    "status": "healthy",
                    "backend": "ray_serve",
                    "ray_serve_health": health_result
                }
            except Exception as e:
                return {
                    "status": "unhealthy",
                    "backend": "ray_serve",
                    "error": str(e)
                }
        else:
            # Check direct wrapper
            return {
                "status": "healthy",
                "backend": "direct_wrapper",
                "device": tts.device if tts else "unknown"
            }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }


@app.get("/")
def root():
    logger.info("Endpoint '/' called.")
    backend_info = "Ray Serve" if USE_RAY_SERVE else "Direct Wrapper"
    return {
        "message": "Welcome to the F5-TTS Web API with Ray Serve Support",
        "version": "3.0",
        "backend": backend_info,
        "ray_available": RAY_AVAILABLE,
        "features": [
            "Ray Serve scalable deployment",
            "Direct wrapper fallback",
            "Enhanced synthesis with infer_gradio structure",
            "Multiple model support (F5-TTS_v1, E2-TTS)",
            "Advanced parameters (speed, cross_fade_duration, nfe_step)",
            "Silence removal",
            "Health monitoring"
        ],
        "endpoints": {
            "/synthesize/": "Enhanced synthesis with Ray Serve or direct wrapper",
            "/health": "Health check endpoint",
            "/": "API information"
        }
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
