#!/usr/bin/env python3
"""
Deploy F5-TTS to existing Ray cluster

This script deploys the F5-TTS service to your existing Ray cluster at 127.0.0.1:8265.
It's designed to work alongside your other Ray projects.
"""

import os
import sys
import time
import logging
import yaml

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    import ray
    from ray import serve
    from ray.serve.config import HTTPOptions
except ImportError:
    print("❌ Error: Ray is not installed. Please install ray[serve] first.")
    print("   pip install ray[serve]")
    sys.exit(1)

from f5_tts_api.f5tts_wrapper import F5TTSServeWrapper, RAY_AVAILABLE

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def connect_to_existing_cluster():
    """Connect to the existing Ray cluster"""
    try:
        # Connect to existing cluster using auto-discovery
        ray.init(address="auto", ignore_reinit_error=True)
        logger.info("✅ Connected to existing Ray cluster")
        
        # Print cluster info
        cluster_resources = ray.cluster_resources()
        logger.info(f"📊 Cluster resources: {cluster_resources}")
        
        # Check if we have GPU resources
        gpu_count = cluster_resources.get('GPU', 0)
        if gpu_count < 1:
            logger.warning("⚠️  No GPU resources found in cluster")
            return False
        
        logger.info(f"🎮 Found {gpu_count} GPU(s) in cluster")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to connect to Ray cluster: {e}")
        logger.info("💡 Make sure your Ray cluster is running and accessible")
        return False


def deploy_f5tts_to_existing_cluster():
    """Deploy F5-TTS service to the existing Ray cluster"""
    try:
        if not RAY_AVAILABLE:
            logger.error("❌ Ray is not available")
            return False
        
        logger.info("🚀 Starting F5-TTS deployment...")
        
        # Check if Ray Serve is already running
        try:
            serve_status = serve.status()
            logger.info("📡 Ray Serve is already running")
        except Exception:
            # Start Ray Serve if not running
            logger.info("🔧 Starting Ray Serve...")
            serve.start(detached=True)
            time.sleep(2)
        
        # Create the deployment with specific resource requirements
        logger.info("🏗️  Creating F5-TTS deployment...")
        
        deployment = F5TTSServeWrapper.options(
            name="f5tts-service",
            num_replicas=1,  # Start with 1 replica
            ray_actor_options={
                "num_cpus": 5,           # Use 5 CPU cores as requested
                "num_gpus": 1            # Use 1 GPU
            },
            max_ongoing_requests=10,
            health_check_period_s=10,
            health_check_timeout_s=30,
        ).bind(
            model_type="F5-TTS_v1",
            device="cuda:0"  # Use cuda:0 as requested
        )
        
        # Deploy the service
        logger.info("📦 Deploying F5-TTS service...")
        serve.run(
            deployment, 
            name="f5tts-service",
            route_prefix="/f5tts"
        )
        
        logger.info("⏳ Waiting for deployment to be ready...")
        time.sleep(15)  # Give it time to initialize
        
        # Check deployment status
        try:
            status = serve.status()
            apps = status.applications
            
            if "f5tts-service" in apps:
                app_status = apps["f5tts-service"].status
                logger.info(f"📊 Deployment status: {app_status}")
                
                if app_status == "RUNNING":
                    logger.info("✅ F5-TTS service is running successfully!")
                    return True
                else:
                    logger.warning(f"⚠️  Deployment status is {app_status}")
                    return False
            else:
                logger.error("❌ Deployment not found in status")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error checking deployment status: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to deploy F5-TTS service: {e}")
        return False


def print_success_info():
    """Print success information and usage examples"""
    print("\n" + "="*70)
    print("🎉 F5-TTS Ray Serve Deployment Successful!")
    print("="*70)
    print(f"🌐 Service URL: http://127.0.0.1:8000/f5tts")
    print(f"📊 Ray Dashboard: http://127.0.0.1:8265")
    print(f"❤️  Health Check: http://127.0.0.1:8000/f5tts/health_check")
    print("\n📝 Example usage:")
    print("curl -X POST http://127.0.0.1:8000/f5tts/synthesize \\")
    print("  -F 'ref_audio=@reference.wav' \\")
    print("  -F 'gen_text=Hello, this is a test synthesis.'")
    print("\n🔧 To use with your FastAPI app:")
    print("export USE_RAY_SERVE=true")
    print("python main_ray.py")
    print("\n📈 Monitor your deployment:")
    print("- Check Ray dashboard for resource usage")
    print("- Use /health endpoint for service status")
    print("- Scale replicas as needed through Ray Serve")
    print("="*70)


def main():
    """Main deployment function"""
    logger.info("🚀 Starting F5-TTS deployment to existing Ray cluster...")
    
    # Connect to existing Ray cluster
    if not connect_to_existing_cluster():
        logger.error("❌ Cannot proceed without Ray cluster connection")
        sys.exit(1)
    
    # Deploy F5-TTS service
    if deploy_f5tts_to_existing_cluster():
        print_success_info()
        
        # Optional: Test the deployment
        logger.info("🧪 Testing deployment...")
        try:
            import requests
            response = requests.get("http://127.0.0.1:8000/f5tts/health_check", timeout=10)
            if response.status_code == 200:
                logger.info("✅ Health check passed!")
            else:
                logger.warning(f"⚠️  Health check returned status {response.status_code}")
        except Exception as e:
            logger.warning(f"⚠️  Could not test deployment: {e}")
        
    else:
        logger.error("❌ Failed to deploy F5-TTS service")
        sys.exit(1)


if __name__ == "__main__":
    main()
