# F5-TTS Ray Serve Deployment Guide

This guide explains how to deploy F5-TTS using Ray Serve to scale your inference workload using GPU cuda:0 and 5 CPU cores.

## Prerequisites

- Ray cluster running at 127.0.0.1:8265 (your existing setup)
- GPU cuda:0 available
- 5 CPU cores available
- Python environment with F5-TTS dependencies

## Quick Deployment

### 1. Deploy to Existing Ray Cluster

```bash
# Deploy F5-TTS to your existing Ray cluster
python deploy_to_existing_ray.py
```

This script will:
- Connect to your existing Ray cluster at 127.0.0.1:8265
- Deploy F5-TTS service with 1 GPU (cuda:0) and 5 CPU cores
- Make the service available at http://127.0.0.1:8000/f5tts

### 2. Start the FastAPI Server (Optional)

If you want to use the enhanced FastAPI interface:

```bash
# Set environment variable to use Ray Serve
export USE_RAY_SERVE=true

# Start the FastAPI server
python main_ray.py
```

This will start a FastAPI server on port 8001 that routes requests to your Ray Serve deployment.

## Service Endpoints

### Ray Serve Direct Endpoints
- **Service URL**: http://127.0.0.1:8000/f5tts
- **Health Check**: http://127.0.0.1:8000/f5tts/health_check

### FastAPI Wrapper Endpoints (if using main_ray.py)
- **Synthesis**: http://127.0.0.1:8001/synthesize/
- **Health**: http://127.0.0.1:8001/health
- **Info**: http://127.0.0.1:8001/

## Usage Examples

### Direct Ray Serve API

```bash
# Synthesize speech using Ray Serve
curl -X POST http://127.0.0.1:8000/f5tts/synthesize \
  -F 'ref_audio=@reference.wav' \
  -F 'gen_text=Hello, this is a test synthesis.'
```

### FastAPI Wrapper

```bash
# Synthesize speech using FastAPI wrapper
curl -X POST http://127.0.0.1:8001/synthesize/ \
  -F 'ref_audio=@reference.wav' \
  -F 'gen_text=Hello, this is a test synthesis.' \
  -F 'model_type=F5-TTS_v1' \
  -F 'speed=1.0'
```

## Resource Configuration

The deployment is configured to use:
- **GPU**: 1 GPU (cuda:0)
- **CPU**: 5 cores
- **Replicas**: 1 (can be scaled)
- **Max Concurrent Queries**: 10

## Monitoring

### Ray Dashboard
Visit http://127.0.0.1:8265 to monitor:
- Resource usage (GPU/CPU)
- Request throughput
- Service health
- Scaling metrics

### Health Checks
```bash
# Check service health
curl http://127.0.0.1:8000/f5tts/health_check

# Check FastAPI wrapper health
curl http://127.0.0.1:8001/health
```

## Scaling

### Manual Scaling
```python
import ray
from ray import serve

# Connect to cluster
ray.init(address="auto")

# Scale to 2 replicas
serve.get_deployment("f5tts-service").options(num_replicas=2).deploy()
```

### Auto-scaling (Advanced)
Modify the deployment configuration in `deploy_to_existing_ray.py`:

```python
deployment = F5TTSServeDeployment.options(
    autoscaling_config={
        "min_replicas": 1,
        "max_replicas": 3,
        "target_num_ongoing_requests_per_replica": 2,
    }
)
```

## Troubleshooting

### Common Issues

1. **Connection Failed**
   ```
   Error: Failed to connect to Ray cluster
   ```
   - Ensure Ray cluster is running
   - Check if port 10001 is accessible

2. **GPU Not Available**
   ```
   Warning: No GPU resources found in cluster
   ```
   - Verify GPU is available: `nvidia-smi`
   - Check Ray cluster GPU resources

3. **Deployment Failed**
   ```
   Error: Failed to deploy F5-TTS service
   ```
   - Check Ray dashboard for errors
   - Verify sufficient resources available

### Debug Commands

```bash
# Check Ray cluster status
ray status

# Check Ray Serve status
python -c "import ray; from ray import serve; ray.init(address='auto'); print(serve.status())"

# Check GPU availability
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

## Configuration Files

- `config/ray_serve_config.yaml`: Ray Serve configuration
- `deploy_to_existing_ray.py`: Deployment script
- `main_ray.py`: FastAPI wrapper with Ray integration

## Integration with Existing Projects

This deployment is designed to work alongside your existing Ray projects. It:
- Uses your existing Ray cluster
- Doesn't interfere with other deployments
- Can be scaled independently
- Shares cluster resources efficiently

The service will appear in your Ray dashboard alongside your other projects.
