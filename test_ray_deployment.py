#!/usr/bin/env python3
"""
Test script for F5-TTS Ray Serve deployment

This script tests the deployed F5-TTS service to ensure it's working correctly.
"""

import os
import sys
import time
import requests
import tempfile
import soundfile as sf
import numpy as np

def create_test_audio():
    """Create a simple test audio file"""
    # Generate a simple sine wave for testing
    sample_rate = 22050
    duration = 2.0  # 2 seconds
    frequency = 440  # A4 note
    
    t = np.linspace(0, duration, int(sample_rate * duration))
    audio = 0.3 * np.sin(2 * np.pi * frequency * t)
    
    # Save to temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
    sf.write(temp_file.name, audio, sample_rate)
    
    return temp_file.name

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    try:
        response = requests.get("http://127.0.0.1:8000/f5tts/health_check", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health check passed: {health_data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ Health check failed with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_synthesis():
    """Test the synthesis endpoint"""
    print("🎵 Testing synthesis...")
    
    # Create test audio
    test_audio_path = create_test_audio()
    
    try:
        # Prepare test data
        files = {
            'ref_audio': open(test_audio_path, 'rb')
        }
        data = {
            'gen_text': 'Hello, this is a test synthesis using Ray Serve.',
            'ref_text': 'This is a reference audio for testing.',
            'model_type': 'F5-TTS_v1',
            'speed': 1.0
        }
        
        print("📤 Sending synthesis request...")
        response = requests.post(
            "http://127.0.0.1:8000/f5tts/synthesize",
            files=files,
            data=data,
            timeout=60  # Give it time for model inference
        )
        
        files['ref_audio'].close()
        
        if response.status_code == 200:
            # Save the response audio
            output_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ Synthesis successful! Output saved to: {output_path}")
            
            # Check if the output file is valid
            try:
                audio_data, sample_rate = sf.read(output_path)
                duration = len(audio_data) / sample_rate
                print(f"📊 Generated audio: {duration:.2f}s at {sample_rate}Hz")
                return True
            except Exception as e:
                print(f"⚠️  Output file seems invalid: {e}")
                return False
        else:
            print(f"❌ Synthesis failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Synthesis test failed: {e}")
        return False
    finally:
        # Clean up test audio
        try:
            os.unlink(test_audio_path)
        except:
            pass

def test_ray_serve_status():
    """Test Ray Serve status"""
    print("📡 Testing Ray Serve status...")
    try:
        import ray
        from ray import serve
        
        # Connect to Ray
        ray.init(address="auto", ignore_reinit_error=True)
        
        # Get serve status
        status = serve.status()
        apps = status.applications
        
        if "f5tts-service" in apps:
            app_status = apps["f5tts-service"].status
            print(f"✅ F5-TTS service status: {app_status}")
            
            # Get deployment details
            deployments = apps["f5tts-service"].deployments
            if "f5tts-service" in deployments:
                deployment = deployments["f5tts-service"]
                print(f"📊 Replicas: {deployment.replica_states}")
                print(f"📊 Status: {deployment.status}")
            
            return app_status == "RUNNING"
        else:
            print("❌ F5-TTS service not found in Ray Serve")
            return False
            
    except Exception as e:
        print(f"❌ Ray Serve status check failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Starting F5-TTS Ray Serve deployment tests...")
    print("="*60)
    
    tests = [
        ("Ray Serve Status", test_ray_serve_status),
        ("Health Check", test_health_check),
        ("Synthesis", test_synthesis),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test passed")
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test error: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n" + "="*60)
    print("📋 Test Results Summary:")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Your F5-TTS Ray Serve deployment is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the deployment.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
