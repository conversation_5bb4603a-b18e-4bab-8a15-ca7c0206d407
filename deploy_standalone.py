#!/usr/bin/env python3
"""
Standalone F5-TTS Ray Serve deployment

This script creates a self-contained deployment without external module dependencies.
"""

import os
import sys
import time
import logging
import tempfile
from typing import Dict, Any, Optional
import asyncio

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

try:
    import ray
    from ray import serve
    import torch
    import soundfile as sf
    import numpy as np
except ImportError as e:
    print(f"❌ Missing required packages: {e}")
    sys.exit(1)

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


@serve.deployment(
    name="f5tts-service",
    num_replicas=1,
    ray_actor_options={
        "num_cpus": 5,
        "num_gpus": 1
    },
    max_ongoing_requests=10,
    health_check_period_s=10,
    health_check_timeout_s=30,
)
class F5TTSService:
    """Standalone F5-TTS service for Ray Serve"""
    
    def __init__(self, model_type: str = "F5-TTS_v1", device: str = "cuda:0"):
        """Initialize F5-TTS service"""
        self.model_type = model_type
        self.device = device
        
        # Set CUDA device
        if torch.cuda.is_available() and device.startswith("cuda"):
            torch.cuda.set_device(device)
        
        # Import F5TTS components
        from f5_tts_api.f5tts_wrapper import F5TTSWrapper
        
        # Initialize wrapper
        self.wrapper = F5TTSWrapper(model_type=model_type, device=device)
        
        # Warm up the model
        self._warmup()
        
        logger.info(f"F5-TTS service initialized on {device}")
    
    def _warmup(self):
        """Warm up the model"""
        try:
            model = self.wrapper.get_model(self.model_type)
            vocoder = self.wrapper._load_vocoder()
            logger.info(f"Model warmed up successfully on {self.device}")
        except Exception as e:
            logger.warning(f"Model warmup failed: {e}")
    
    async def synthesize(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Synthesize speech"""
        try:
            # Extract parameters
            ref_audio_path = request_data.get("ref_audio_path")
            ref_text = request_data.get("ref_text", "")
            gen_text = request_data.get("gen_text")
            model_type = request_data.get("model_type", self.model_type)
            remove_silence = request_data.get("remove_silence", False)
            seed = request_data.get("seed", -1)
            cross_fade_duration = request_data.get("cross_fade_duration", 0.15)
            nfe_step = request_data.get("nfe_step", 32)
            speed = request_data.get("speed", 1.0)
            
            # Run synthesis in thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._run_synthesis,
                ref_audio_path, ref_text, gen_text, model_type,
                remove_silence, seed, cross_fade_duration, nfe_step, speed
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Synthesis failed: {e}")
            return {"error": str(e), "success": False}
    
    def _run_synthesis(self, ref_audio_path, ref_text, gen_text, model_type,
                      remove_silence, seed, cross_fade_duration, nfe_step, speed):
        """Run synthesis synchronously"""
        try:
            # Run inference
            (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = self.wrapper.infer(
                ref_audio_orig=ref_audio_path,
                ref_text=ref_text,
                gen_text=gen_text,
                model_type=model_type,
                remove_silence=remove_silence,
                seed=seed,
                cross_fade_duration=cross_fade_duration,
                nfe_step=nfe_step,
                speed=speed,
                show_info=logger.info,
            )
            
            # Save audio to temporary file
            output_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
            sf.write(output_path, audio_data, sample_rate)
            
            return {
                "success": True,
                "output_path": output_path,
                "sample_rate": sample_rate,
                "spectrogram_path": spectrogram_path,
                "processed_ref_text": processed_ref_text,
                "used_seed": used_seed,
                "audio_length": len(audio_data) / sample_rate
            }
            
        except Exception as e:
            logger.error(f"Synthesis execution failed: {e}")
            return {"error": str(e), "success": False}
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check endpoint"""
        try:
            cuda_available = torch.cuda.is_available()
            if cuda_available and self.device.startswith("cuda"):
                device_id = int(self.device.split(":")[1])
                gpu_memory = torch.cuda.get_device_properties(device_id).total_memory
                gpu_memory_allocated = torch.cuda.memory_allocated(device_id)
            else:
                gpu_memory = gpu_memory_allocated = 0
            
            return {
                "status": "healthy",
                "device": self.device,
                "model_type": self.model_type,
                "cuda_available": cuda_available,
                "gpu_memory_total": gpu_memory,
                "gpu_memory_allocated": gpu_memory_allocated,
            }
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}


def connect_to_ray():
    """Connect to Ray cluster"""
    try:
        ray.init(address="auto", ignore_reinit_error=True)
        logger.info("Connected to Ray cluster")
        
        cluster_resources = ray.cluster_resources()
        logger.info(f"Cluster resources: {cluster_resources}")
        
        gpu_count = cluster_resources.get('GPU', 0)
        if gpu_count < 1:
            logger.warning("No GPU resources found in cluster")
            return False
        
        logger.info(f"Found {gpu_count} GPU(s) in cluster")
        return True
        
    except Exception as e:
        logger.error(f"Failed to connect to Ray cluster: {e}")
        return False


def deploy_service():
    """Deploy the F5-TTS service"""
    try:
        # Start Ray Serve if not running
        try:
            serve.status()
            logger.info("Ray Serve is already running")
        except Exception:
            logger.info("Starting Ray Serve...")
            serve.start(detached=True)
            time.sleep(2)
        
        # Create and deploy the service
        logger.info("Deploying F5-TTS service...")
        
        deployment = F5TTSService.bind(
            model_type="F5-TTS_v1",
            device="cuda:0"
        )
        
        serve.run(deployment, name="f5tts-service", route_prefix="/f5tts")
        
        # Wait for deployment
        logger.info("Waiting for deployment to be ready...")
        time.sleep(15)
        
        # Check status
        status = serve.status()
        apps = status.applications
        
        if "f5tts-service" in apps:
            app_status = apps["f5tts-service"].status
            logger.info(f"Deployment status: {app_status}")
            
            if app_status == "RUNNING":
                logger.info("✅ F5-TTS service deployed successfully!")
                return True
            else:
                logger.warning(f"Deployment status is {app_status}")
                return False
        else:
            logger.error("Deployment not found")
            return False
            
    except Exception as e:
        logger.error(f"Deployment failed: {e}")
        return False


def main():
    """Main function"""
    logger.info("🚀 Starting F5-TTS standalone deployment...")
    
    # Connect to Ray
    if not connect_to_ray():
        logger.error("Failed to connect to Ray cluster")
        sys.exit(1)
    
    # Deploy service
    if deploy_service():
        print("\n" + "="*60)
        print("🎉 F5-TTS Service Deployed Successfully!")
        print("="*60)
        print("🌐 Service URL: http://127.0.0.1:8000/f5tts")
        print("📊 Ray Dashboard: http://127.0.0.1:8265")
        print("❤️  Health Check: http://127.0.0.1:8000/f5tts/health_check")
        print("\n📝 Example usage:")
        print("curl -X POST http://127.0.0.1:8000/f5tts/synthesize \\")
        print("  -F 'ref_audio=@reference.wav' \\")
        print("  -F 'gen_text=Hello, this is a test synthesis.'")
        print("="*60)
        
        # Test health check
        try:
            import requests
            response = requests.get("http://127.0.0.1:8000/f5tts/health_check", timeout=10)
            if response.status_code == 200:
                logger.info("✅ Health check passed!")
            else:
                logger.warning(f"Health check returned status {response.status_code}")
        except Exception as e:
            logger.warning(f"Could not test health check: {e}")
        
    else:
        logger.error("❌ Deployment failed")
        sys.exit(1)


if __name__ == "__main__":
    main()
