import json
import os
import tempfile
from functools import lru_cache
import asyncio
from typing import Dict, Any, Tu<PERSON>, Optional

import numpy as np
import soundfile as sf
import torch
import torchaudio
from cached_path import cached_path

from f5_tts.infer.utils_infer import (
    infer_process,
    load_model,
    load_vocoder,
    preprocess_ref_audio_text,
    remove_silence_for_generated_wav,
    save_spectrogram,
    tempfile_kwargs,
)
from f5_tts.model import DiT, UNetT

try:
    import ray
    from ray import serve
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False


class SimpleProgress:
    """Simple progress wrapper that mimics gr.Progress() for API usage"""
    def tqdm(self, iterable):
        """Simple tqdm wrapper that just returns the iterable"""
        return iterable


# Default model configurations
DEFAULT_TTS_MODEL = "F5-TTS_v1"
DEFAULT_TTS_MODEL_CFG = [
    "hf://SWivid/F5-TTS/F5TTS_v1_Base/model_1250000.safetensors",
    "hf://SWivid/F5-TTS/F5TTS_v1_Base/vocab.txt",
    json.dumps(dict(dim=1024, depth=22, heads=16, ff_mult=2, text_dim=512, conv_layers=4)),
]


class F5TTSWrapper:
    def __init__(
        self,
        model_type="F5-TTS_v1",
        device=None,
        hf_cache_dir=None,
    ):
        """
        Initialize F5TTS Wrapper following infer_gradio structure

        Args:
            model_type: Model type to use ("F5-TTS_v1", "E2-TTS", or custom config)
            device: Device to use for inference
            hf_cache_dir: Hugging Face cache directory
        """
        # Set device
        self.device = (
            device
            or "cuda" if torch.cuda.is_available()
            else "mps" if torch.backends.mps.is_available()
            else "cpu"
        )

        self.hf_cache_dir = hf_cache_dir
        self.model_type = model_type

        # Initialize model storage (lazy loading)
        self.vocoder = None
        self.f5tts_model = None
        self.e2tts_model = None
        self.custom_model = None
        self.custom_model_path = ""

    def _load_vocoder(self):
        """Load vocoder lazily"""
        if self.vocoder is None:
            self.vocoder = load_vocoder()
        return self.vocoder

    def _load_f5tts(self):
        """Load F5TTS model following infer_gradio pattern"""
        ckpt_path = str(cached_path(DEFAULT_TTS_MODEL_CFG[0], cache_dir=self.hf_cache_dir))
        f5tts_model_cfg = json.loads(DEFAULT_TTS_MODEL_CFG[2])
        return load_model(DiT, f5tts_model_cfg, ckpt_path)

    def _load_e2tts(self):
        """Load E2TTS model following infer_gradio pattern"""
        ckpt_path = str(cached_path("hf://SWivid/E2-TTS/E2TTS_Base/model_1200000.safetensors", cache_dir=self.hf_cache_dir))
        e2tts_model_cfg = dict(dim=1024, depth=24, heads=16, ff_mult=4, text_mask_padding=False, pe_attn_head=1)
        return load_model(UNetT, e2tts_model_cfg, ckpt_path)

    def _load_custom(self, ckpt_path: str, vocab_path="", model_cfg=None):
        """Load custom model following infer_gradio pattern"""
        ckpt_path, vocab_path = ckpt_path.strip(), vocab_path.strip()
        if ckpt_path.startswith("hf://"):
            ckpt_path = str(cached_path(ckpt_path, cache_dir=self.hf_cache_dir))
        if vocab_path.startswith("hf://"):
            vocab_path = str(cached_path(vocab_path, cache_dir=self.hf_cache_dir))
        if model_cfg is None:
            model_cfg = json.loads(DEFAULT_TTS_MODEL_CFG[2])
        elif isinstance(model_cfg, str):
            model_cfg = json.loads(model_cfg)
        return load_model(DiT, model_cfg, ckpt_path, vocab_file=vocab_path)

    def get_model(self, model_type=None, custom_ckpt_path=None, custom_vocab_path="", custom_model_cfg=None):
        """Get the appropriate model based on type, following infer_gradio pattern"""
        if model_type is None:
            model_type = self.model_type

        if model_type == "F5-TTS_v1" or model_type == DEFAULT_TTS_MODEL:
            if self.f5tts_model is None:
                self.f5tts_model = self._load_f5tts()
            return self.f5tts_model
        elif model_type == "E2-TTS":
            if self.e2tts_model is None:
                self.e2tts_model = self._load_e2tts()
            return self.e2tts_model
        elif isinstance(model_type, tuple) and model_type[0] == "Custom":
            # Custom model handling
            if self.custom_model_path != model_type[1]:
                self.custom_model = self._load_custom(
                    model_type[1],
                    vocab_path=model_type[2] if len(model_type) > 2 else "",
                    model_cfg=model_type[3] if len(model_type) > 3 else None
                )
                self.custom_model_path = model_type[1]
            return self.custom_model
        elif custom_ckpt_path:
            # Direct custom model loading
            if self.custom_model_path != custom_ckpt_path:
                self.custom_model = self._load_custom(custom_ckpt_path, custom_vocab_path, custom_model_cfg)
                self.custom_model_path = custom_ckpt_path
            return self.custom_model
        else:
            raise ValueError(f"Unknown model type: {model_type}")

    @lru_cache(maxsize=100)
    def infer(
        self,
        ref_audio_orig,
        ref_text,
        gen_text,
        model_type=None,
        remove_silence=False,
        seed=-1,
        cross_fade_duration=0.15,
        nfe_step=32,
        speed=1.0,
        show_info=print,
    ):
        """
        Main inference function following infer_gradio pattern

        Args:
            ref_audio_orig: Path to reference audio file
            ref_text: Reference text (if empty, will be transcribed)
            gen_text: Text to generate
            model_type: Model type to use
            remove_silence: Whether to remove silence from output
            seed: Random seed (-1 for random)
            cross_fade_duration: Cross-fade duration between segments
            nfe_step: Number of denoising steps
            speed: Speed multiplier
            show_info: Function to show info messages

        Returns:
            Tuple of (sample_rate, audio_data), spectrogram_path, ref_text, used_seed
        """
        if not ref_audio_orig:
            raise ValueError("Please provide reference audio.")

        # Set inference seed
        if seed < 0 or seed > 2**31 - 1:
            seed = np.random.randint(0, 2**31 - 1)
        torch.manual_seed(seed)
        used_seed = seed

        if not gen_text.strip():
            raise ValueError("Please enter text to generate.")

        # Preprocess reference audio and text
        ref_audio, ref_text = preprocess_ref_audio_text(ref_audio_orig, ref_text, show_info=show_info)

        # Get the appropriate model and vocoder
        ema_model = self.get_model(model_type)
        vocoder = self._load_vocoder()

        # Run inference
        final_wave, final_sample_rate, combined_spectrogram = infer_process(
            ref_audio,
            ref_text,
            gen_text,
            ema_model,
            vocoder,
            cross_fade_duration=cross_fade_duration,
            nfe_step=nfe_step,
            speed=speed,
            show_info=show_info,
            progress=SimpleProgress(),  # Use simple progress wrapper for API usage
        )

        # Remove silence if requested
        if remove_silence:
            with tempfile.NamedTemporaryFile(suffix=".wav", **tempfile_kwargs) as f:
                temp_path = f.name
            try:
                sf.write(temp_path, final_wave, final_sample_rate)
                remove_silence_for_generated_wav(temp_path)
                final_wave, _ = torchaudio.load(temp_path)
            finally:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
            final_wave = final_wave.squeeze().cpu().numpy()

        # Save the spectrogram
        spectrogram_path = None
        if combined_spectrogram is not None:
            with tempfile.NamedTemporaryFile(suffix=".png", **tempfile_kwargs) as tmp_spectrogram:
                spectrogram_path = tmp_spectrogram.name
            save_spectrogram(combined_spectrogram, spectrogram_path)

        return (final_sample_rate, final_wave), spectrogram_path, ref_text, used_seed

    def synthesize(self, ref_file, ref_text, gen_text, seed=None, **kwargs):
        """
        Legacy  synthesize method for backward compatibility

        Args:
            ref_file: Path to reference audio file
            ref_text: Reference text
            gen_text: Text to generate
            seed: Random seed
            **kwargs: Additional parameters for infer method

        Returns:
            Tuple of (audio_data, sample_rate, seed)
        """
        if seed is None:
            seed = np.random.randint(0, 2**31 - 1)

        # Call the new infer method
        (sample_rate, audio_data), _, _, used_seed = self.infer(
            ref_file,
            ref_text,
            gen_text,
            seed=seed,
            **kwargs
        )

        return audio_data, sample_rate, used_seed


# Ray Serve Deployment Class
if RAY_AVAILABLE:
    @serve.deployment(
        name="f5tts-service",
        num_replicas=1,
        ray_actor_options={
            "num_cpus": 5,
            "num_gpus": 1,
            "resources": {"GPU": 1}
        },
        max_concurrent_queries=10,
        health_check_period_s=10,
        health_check_timeout_s=30,
    )
    class F5TTSServeDeployment:
        """Ray Serve deployment wrapper for F5TTSWrapper"""

        def __init__(self, model_type: str = "F5-TTS_v1", device: str = "cuda:0"):
            """Initialize the Ray Serve deployment with F5TTSWrapper"""
            self.device = device
            self.model_type = model_type

            # Set CUDA device
            if torch.cuda.is_available() and device.startswith("cuda"):
                torch.cuda.set_device(device)

            # Initialize the wrapper
            self.tts_wrapper = F5TTSWrapper(
                model_type=model_type,
                device=device
            )

            # Warm up the model by loading it
            self._warmup()

        def _warmup(self):
            """Warm up the model by loading it into memory"""
            try:
                # Load the default model to warm up
                model = self.tts_wrapper.get_model(self.model_type)
                vocoder = self.tts_wrapper._load_vocoder()
                print(f"F5-TTS model warmed up successfully on {self.device}")
            except Exception as e:
                print(f"Warning: Model warmup failed: {e}")

        async def synthesize(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
            """
            Async synthesis method for Ray Serve

            Args:
                request_data: Dictionary containing synthesis parameters

            Returns:
                Dictionary with synthesis results
            """
            try:
                # Extract parameters from request
                ref_audio_path = request_data.get("ref_audio_path")
                ref_text = request_data.get("ref_text", "")
                gen_text = request_data.get("gen_text")
                model_type = request_data.get("model_type", self.model_type)
                remove_silence = request_data.get("remove_silence", False)
                seed = request_data.get("seed", -1)
                cross_fade_duration = request_data.get("cross_fade_duration", 0.15)
                nfe_step = request_data.get("nfe_step", 32)
                speed = request_data.get("speed", 1.0)

                # Run synthesis in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    None,
                    self._run_synthesis,
                    ref_audio_path,
                    ref_text,
                    gen_text,
                    model_type,
                    remove_silence,
                    seed,
                    cross_fade_duration,
                    nfe_step,
                    speed
                )

                return result

            except Exception as e:
                return {
                    "error": str(e),
                    "success": False
                }

        def _run_synthesis(
            self,
            ref_audio_path: str,
            ref_text: str,
            gen_text: str,
            model_type: str,
            remove_silence: bool,
            seed: int,
            cross_fade_duration: float,
            nfe_step: int,
            speed: float
        ) -> Dict[str, Any]:
            """
            Run synthesis synchronously

            Returns:
                Dictionary with synthesis results
            """
            try:
                # Run inference
                (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = self.tts_wrapper.infer(
                    ref_audio_orig=ref_audio_path,
                    ref_text=ref_text,
                    gen_text=gen_text,
                    model_type=model_type,
                    remove_silence=remove_silence,
                    seed=seed,
                    cross_fade_duration=cross_fade_duration,
                    nfe_step=nfe_step,
                    speed=speed,
                    show_info=print,
                )

                # Save audio to temporary file
                output_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
                sf.write(output_path, audio_data, sample_rate)

                return {
                    "success": True,
                    "output_path": output_path,
                    "sample_rate": sample_rate,
                    "spectrogram_path": spectrogram_path,
                    "processed_ref_text": processed_ref_text,
                    "used_seed": used_seed,
                    "audio_length": len(audio_data) / sample_rate
                }

            except Exception as e:
                return {
                    "error": str(e),
                    "success": False
                }

        async def health_check(self) -> Dict[str, Any]:
            """Health check endpoint for Ray Serve"""
            try:
                # Check if CUDA is available and accessible
                cuda_available = torch.cuda.is_available()
                if cuda_available and self.device.startswith("cuda"):
                    device_id = int(self.device.split(":")[1])
                    gpu_memory = torch.cuda.get_device_properties(device_id).total_memory
                    gpu_memory_allocated = torch.cuda.memory_allocated(device_id)
                    gpu_memory_cached = torch.cuda.memory_reserved(device_id)
                else:
                    gpu_memory = gpu_memory_allocated = gpu_memory_cached = 0

                return {
                    "status": "healthy",
                    "device": self.device,
                    "model_type": self.model_type,
                    "cuda_available": cuda_available,
                    "gpu_memory_total": gpu_memory,
                    "gpu_memory_allocated": gpu_memory_allocated,
                    "gpu_memory_cached": gpu_memory_cached,
                    "timestamp": torch.cuda.Event().query() if cuda_available else None
                }
            except Exception as e:
                return {
                    "status": "unhealthy",
                    "error": str(e)
                }

    # Create deployment handle for external access
    f5tts_deployment = F5TTSServeDeployment.bind(
        model_type="F5-TTS_v1",
        device="cuda:0"
    )
else:
    # Fallback if Ray is not available
    F5TTSServeDeployment = None
    f5tts_deployment = None
