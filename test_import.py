#!/usr/bin/env python3
"""
Test script to verify imports work correctly before deployment
"""

import os
import sys

# Add the src directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, current_dir)

def test_imports():
    """Test all required imports"""
    try:
        print("🔍 Testing imports...")
        
        # Test Ray imports
        import ray
        from ray import serve
        print("✅ Ray imports successful")
        
        # Test F5TTS imports
        from f5_tts_api.f5tts_wrapper import F5TTSWrapper
        print("✅ F5TTSWrapper import successful")
        
        # Test torch
        import torch
        print(f"✅ Torch import successful - CUDA available: {torch.cuda.is_available()}")
        
        # Test creating wrapper
        wrapper = F5TTSWrapper(model_type="F5-TTS_v1", device="cuda:0" if torch.cuda.is_available() else "cpu")
        print("✅ F5TTSWrapper creation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_ray_connection():
    """Test Ray cluster connection"""
    try:
        print("🔍 Testing Ray connection...")
        
        import ray
        ray.init(address="auto", ignore_reinit_error=True)
        
        cluster_resources = ray.cluster_resources()
        print(f"✅ Ray cluster connected - Resources: {cluster_resources}")
        
        gpu_count = cluster_resources.get('GPU', 0)
        if gpu_count >= 1:
            print(f"✅ GPU resources available: {gpu_count}")
        else:
            print("⚠️  No GPU resources found in cluster")
        
        return True
        
    except Exception as e:
        print(f"❌ Ray connection test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Running pre-deployment tests...")
    print("="*50)
    
    tests = [
        ("Import Test", test_imports),
        ("Ray Connection Test", test_ray_connection),
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name}...")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 All tests passed! Ready for deployment.")
        print("Run: python deploy_to_existing_ray.py")
        return 0
    else:
        print("❌ Some tests failed. Please fix issues before deployment.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
