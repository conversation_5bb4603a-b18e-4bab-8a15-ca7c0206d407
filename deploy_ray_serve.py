#!/usr/bin/env python3
"""
Ray Serve deployment script for F5-TTS API

This script deploys the F5-TTS service to an existing Ray cluster.
It connects to the Ray cluster at 127.0.0.1:8265 and deploys the F5-TTS service.
"""

import os
import sys
import time
import logging
from typing import Optional
import yaml

try:
    import ray
    from ray import serve
    from ray.serve.config import HTTPOptions
except ImportError:
    print("Error: Ray is not installed. Please install ray[serve] first.")
    sys.exit(1)

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from f5_tts_api.f5tts_wrapper import F5TTSServeDeployment, RAY_AVAILABLE

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def load_config(config_path: str = "config/ray_serve_config.yaml") -> dict:
    """Load configuration from YAML file"""
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        return config
    except FileNotFoundError:
        logger.warning(f"Config file {config_path} not found, using defaults")
        return {}
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}


def connect_to_ray_cluster(ray_address: str = "127.0.0.1:10001") -> bool:
    """Connect to existing Ray cluster"""
    try:
        # Try to connect to existing cluster
        ray.init(address=ray_address, ignore_reinit_error=True)
        logger.info(f"Connected to Ray cluster at {ray_address}")
        
        # Print cluster info
        cluster_resources = ray.cluster_resources()
        logger.info(f"Cluster resources: {cluster_resources}")
        
        return True
    except Exception as e:
        logger.error(f"Failed to connect to Ray cluster: {e}")
        return False


def deploy_f5tts_service(config: dict) -> bool:
    """Deploy F5-TTS service to Ray Serve"""
    try:
        if not RAY_AVAILABLE:
            logger.error("Ray is not available")
            return False
        
        # Get configuration
        f5tts_config = config.get('f5tts_service', {})
        ray_serve_config = config.get('ray_serve', {})
        
        # Configure HTTP options
        http_options = HTTPOptions(
            host=ray_serve_config.get('http_host', '0.0.0.0'),
            port=ray_serve_config.get('http_port', 8000),
            root_path=ray_serve_config.get('route_prefix', '/f5tts')
        )
        
        # Start Ray Serve
        serve.start(
            detached=ray_serve_config.get('detached', True),
            http_options=http_options
        )
        logger.info("Ray Serve started successfully")
        
        # Create deployment with configuration
        deployment = F5TTSServeDeployment.options(
            name="f5tts-service",
            num_replicas=f5tts_config.get('min_replicas', 1),
            ray_actor_options={
                "num_cpus": f5tts_config.get('num_cpus_per_replica', 5),
                "num_gpus": f5tts_config.get('num_gpus_per_replica', 1),
                "resources": {"GPU": f5tts_config.get('num_gpus_per_replica', 1)}
            },
            max_concurrent_queries=f5tts_config.get('max_concurrent_queries', 10),
            health_check_period_s=10,
            health_check_timeout_s=30,
        ).bind(
            model_type=f5tts_config.get('default_model', 'F5-TTS_v1'),
            device=f5tts_config.get('gpu_device', 'cuda:0')
        )
        
        # Deploy the service
        serve.run(deployment, name="f5tts-service", route_prefix="/f5tts")
        logger.info("F5-TTS service deployed successfully")
        
        # Wait for deployment to be ready
        logger.info("Waiting for deployment to be ready...")
        time.sleep(10)
        
        # Check deployment status
        deployments = serve.status().applications
        if "f5tts-service" in deployments:
            status = deployments["f5tts-service"].status
            logger.info(f"Deployment status: {status}")
            
            if status == "RUNNING":
                logger.info("✅ F5-TTS service is running successfully!")
                logger.info(f"Service available at: http://127.0.0.1:{ray_serve_config.get('http_port', 8000)}/f5tts")
                return True
            else:
                logger.warning(f"Deployment status is {status}, not RUNNING")
                return False
        else:
            logger.error("Deployment not found in status")
            return False
            
    except Exception as e:
        logger.error(f"Failed to deploy F5-TTS service: {e}")
        return False


def check_deployment_health() -> bool:
    """Check if the deployment is healthy"""
    try:
        import requests
        
        # Try to access the health check endpoint
        response = requests.get("http://127.0.0.1:8000/f5tts/health_check", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            logger.info(f"Health check passed: {health_data}")
            return True
        else:
            logger.warning(f"Health check failed with status {response.status_code}")
            return False
    except Exception as e:
        logger.warning(f"Health check failed: {e}")
        return False


def main():
    """Main deployment function"""
    logger.info("Starting F5-TTS Ray Serve deployment...")
    
    # Load configuration
    config = load_config()
    
    # Connect to Ray cluster
    ray_address = "127.0.0.1:10001"  # Default Ray cluster address
    if not connect_to_ray_cluster(ray_address):
        logger.error("Failed to connect to Ray cluster. Exiting.")
        sys.exit(1)
    
    # Deploy F5-TTS service
    if deploy_f5tts_service(config):
        logger.info("🚀 F5-TTS service deployed successfully!")
        
        # Check health
        time.sleep(5)
        if check_deployment_health():
            logger.info("✅ Service is healthy and ready to serve requests")
        else:
            logger.warning("⚠️  Service deployed but health check failed")
        
        # Print usage information
        print("\n" + "="*60)
        print("F5-TTS Ray Serve Deployment Complete!")
        print("="*60)
        print(f"Service URL: http://127.0.0.1:8000/f5tts")
        print(f"Ray Dashboard: http://127.0.0.1:8265")
        print(f"Health Check: http://127.0.0.1:8000/f5tts/health_check")
        print("\nExample usage:")
        print("curl -X POST http://127.0.0.1:8000/f5tts/synthesize \\")
        print("  -F 'ref_audio=@reference.wav' \\")
        print("  -F 'gen_text=Hello, this is a test synthesis.'")
        print("="*60)
        
    else:
        logger.error("❌ Failed to deploy F5-TTS service")
        sys.exit(1)


if __name__ == "__main__":
    main()
