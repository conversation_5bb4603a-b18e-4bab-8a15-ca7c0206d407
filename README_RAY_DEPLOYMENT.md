# F5-TTS Ray Serve Deployment

This setup scales F5-TTS using Ray Serve with your existing Ray cluster at 127.0.0.1:8265, using 1 GPU (cuda:0) and 5 CPU cores.

## Quick Start

### 1. Deploy to Your Existing Ray Cluster

```bash
python deploy_to_existing_ray.py
```

This will:
- Connect to your existing Ray cluster at 127.0.0.1:8265
- Deploy F5-TTS service using cuda:0 and 5 CPU cores
- Make the service available at http://127.0.0.1:8000/f5tts

### 2. Test the Deployment

```bash
python test_ray_deployment.py
```

### 3. Use the Service

#### Direct API calls:
```bash
curl -X POST http://127.0.0.1:8000/f5tts/synthesize \
  -F 'ref_audio=@reference.wav' \
  -F 'gen_text=Hello, this is a test synthesis.'
```

#### Or use the FastAPI wrapper:
```bash
export USE_RAY_SERVE=true
python main_ray.py
# Then access http://127.0.0.1:8001/synthesize/
```

## Architecture

The deployment uses:
- **F5TTSServeWrapper**: Ray Serve deployment that inherits from F5TTSWrapper
- **Resource allocation**: 1 GPU (cuda:0) + 5 CPU cores per replica
- **Scaling**: Starts with 1 replica, can scale up to handle more requests
- **Integration**: Works alongside your existing Ray projects

## Key Features

✅ **Inherits from F5TTSWrapper**: Direct use of existing class with `@serve.deployment` decorator  
✅ **GPU Optimization**: Uses cuda:0 as requested  
✅ **CPU Allocation**: Uses 5 cores as specified  
✅ **Existing Cluster**: Integrates with your Ray cluster at 127.0.0.1:8265  
✅ **Scalable**: Can handle multiple concurrent requests  
✅ **Health Monitoring**: Built-in health checks and monitoring  

## Files

- `src/f5_tts_api/f5tts_wrapper.py` - Enhanced with Ray Serve deployment
- `deploy_to_existing_ray.py` - Deployment script for existing cluster
- `main_ray.py` - FastAPI wrapper with Ray integration
- `test_ray_deployment.py` - Test script
- `config/ray_serve_config.yaml` - Configuration

## Monitoring

- **Ray Dashboard**: http://127.0.0.1:8265
- **Health Check**: http://127.0.0.1:8000/f5tts/health_check
- **Service Status**: Check Ray dashboard for resource usage and scaling

Your F5-TTS service will appear in the Ray dashboard alongside your other projects!
